import { Canvas, Rect } from "fabric";
import { CropData, TransformState } from "@/shared/types";
import { scaleCanvasObjects } from "../rendering/resize";
import { loadAnnotations } from "./annotations";

import {
  applyCanvasRotation,
  applyCanvasFlipHorizontal,
  applyCanvasFlipVertical,
} from "./transforms";

export const createImageLoadContainer = (
  cropData: CropData,
  fallbackRect?: DOMRect
): DOMRect | undefined => {
  if (!cropData.canvasDimensions) return fallbackRect;

  return {
    width: cropData.canvasDimensions.width,
    height: cropData.canvasDimensions.height,
    x: 0,
    y: 0,
    top: 0,
    left: 0,
    bottom: cropData.canvasDimensions.height,
    right: cropData.canvasDimensions.width,
    toJSON: () => ({}),
  } as DOMRect;
};

export const applyCropToCanvas = async (canvas: Canvas, cropData: CropData): Promise<void> => {
  if (!cropData.normalizedCropRect) return;
  const cropRect = canvas.getObjects().find((obj) => (obj as any).name === "cropRect");
  if (cropRect) {
    canvas.remove(cropRect);
  }
  const canvasWidth = canvas.getWidth();
  const canvasHeight = canvas.getHeight();
  const left = cropData.normalizedCropRect.left * canvasWidth;
  const top = cropData.normalizedCropRect.top * canvasHeight;
  const width = cropData.normalizedCropRect.width * canvasWidth;
  const height = cropData.normalizedCropRect.height * canvasHeight;

  const annotations = canvas
    .getObjects()
    .filter((obj) => (obj as any).name !== "backgroundImage" && (obj as any).name !== "cropRect");
  const previousVisibilities = annotations.map((obj) => obj.visible);
  annotations.forEach((obj) => obj.set({ visible: false }));
  canvas.renderAll();

  const fullCanvasImage = canvas.toDataURL();
  const croppedDataURL = await new Promise<string>((resolve) => {
    const img = new Image();
    img.onload = () => {
      const tempCanvas = document.createElement("canvas");
      tempCanvas.width = width;
      tempCanvas.height = height;
      const ctx = tempCanvas.getContext("2d")!;
      ctx.drawImage(img, left, top, width, height, 0, 0, width, height);
      resolve(tempCanvas.toDataURL());
    };
    img.src = fullCanvasImage;
  });

  annotations.forEach((obj, i) => obj.set({ visible: previousVisibilities[i] }));
  canvas.renderAll();

  const { FabricImage } = await import("fabric");
  const croppedImage = await FabricImage.fromURL(croppedDataURL, {
    crossOrigin: "anonymous",
  });

  canvas.setDimensions({ width, height });
  canvas.backgroundImage = croppedImage;
  croppedImage.set({
    left: width / 2,
    top: height / 2,
    originX: "center",
    originY: "center",
  });

  canvas.forEachObject((obj) => {
    if ((obj as any).name === "backgroundImage") return;
    obj.set({
      left: (obj.left || 0) - left,
      top: (obj.top || 0) - top,
    });
  });

  scaleCanvasObjects(canvas, 1);

  canvas.renderAll();
};

export const createCropHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  hasPerformedCrop: boolean,
  setCropData: (data: CropData) => void,
  isUndoing: React.MutableRefObject<boolean>,
  setHasPerformedCrop: (value: boolean) => void,
  containerRef?: React.RefObject<HTMLElement | null>,
  originalImageUrl?: string,
  savedAnnotations?: any,
  transformState?: TransformState,
  currentCropData?: CropData
) => {
  return async () => {
    if (!fabricCanvas?.current) return;
    const canvas = fabricCanvas.current;

    if (hasPerformedCrop) {
      await restoreCroppedCanvas(
        canvas,
        setCropData,
        setHasPerformedCrop,
        originalImageUrl || "",
        containerRef,
        savedAnnotations,
        transformState,
        currentCropData
      );
      return;
    }

    let cropRect = canvas.getActiveObject();
    if (!(cropRect instanceof Rect) || (cropRect as any).name !== "cropRect") {
      cropRect = canvas.getObjects().find((obj) => (obj as any).name === "cropRect");
    }

    if (!cropRect || !canvas.backgroundImage) return;

    isUndoing.current = true;

    const left = cropRect.left || 0;
    const top = cropRect.top || 0;
    const width = (cropRect.width || 0) * (cropRect.scaleX || 1);
    const height = (cropRect.height || 0) * (cropRect.scaleY || 1);

    const canvasWidth = canvas.getWidth();
    const canvasHeight = canvas.getHeight();

    const normalizedCropRect = {
      left: left / canvasWidth,
      top: top / canvasHeight,
      width: width / canvasWidth,
      height: height / canvasHeight,
    };

    const cropResult: CropData = {
      isCropped: true,
      normalizedCropRect,
      canvasDimensions: containerRef?.current
        ? {
            width: containerRef.current.getBoundingClientRect().width,
            height: containerRef.current.getBoundingClientRect().height,
          }
        : undefined,
      transformStateAtCrop: transformState ? { ...transformState } : undefined,
    };

    await applyCropToCanvas(canvas, cropResult);

    setCropData(cropResult);
    setHasPerformedCrop(true);
    isUndoing.current = false;
  };
};

export const restoreCroppedCanvas = async (
  canvas: Canvas,
  setCropData: (data: CropData) => void,
  setHasPerformedCrop: (value: boolean) => void,
  originalImageUrl: string,
  containerRef?: React.RefObject<HTMLElement | null>,
  savedAnnotations?: any,
  transformState?: TransformState,
  currentCropData?: CropData
) => {
  if (!canvas || !originalImageUrl) return;

  const containerRect = containerRef?.current?.getBoundingClientRect();
  if (!containerRect) return;

  const { FabricImage } = await import("fabric");
  const originalImage = await FabricImage.fromURL(originalImageUrl, {
    crossOrigin: "anonymous",
  });

  const imageAspect = (originalImage.width || 1) / (originalImage.height || 1);
  const containerAspect = containerRect.width / containerRect.height;

  let scale: number;
  let targetWidth: number;
  let targetHeight: number;

  if (imageAspect > containerAspect) {
    targetWidth = containerRect.width;
    targetHeight = containerRect.width / imageAspect;
    scale = targetWidth / (originalImage.width || 1);
  } else {
    targetHeight = containerRect.height;
    targetWidth = targetHeight * imageAspect;
    scale = targetHeight / (originalImage.height || 1);
  }

  canvas.setDimensions({ width: targetWidth, height: targetHeight });

  originalImage.set({
    left: targetWidth / 2,
    top: targetHeight / 2,
    originX: "center",
    originY: "center",
    scaleX: scale,
    scaleY: scale,
  });
  canvas.backgroundImage = originalImage;

  if (savedAnnotations && savedAnnotations.canvasState) {
    await canvas.loadFromJSON(savedAnnotations.canvasState);
    canvas.backgroundImage = originalImage;
  } else if (savedAnnotations) {
    await loadAnnotations(canvas, savedAnnotations);
  }

  const transformsAtCrop = currentCropData?.transformStateAtCrop;
  const currentTransforms = transformState;

  if (transformsAtCrop && currentTransforms) {
    if (transformsAtCrop.rotations) {
      for (let i = 0; i < transformsAtCrop.rotations; i++) {
        applyCanvasRotation(canvas);
      }
    }
    if (transformsAtCrop.flipHorizontal) {
      applyCanvasFlipHorizontal(canvas, transformsAtCrop.rotations);
    }
    if (transformsAtCrop.flipVertical) {
      applyCanvasFlipVertical(canvas, transformsAtCrop.rotations);
    }

    const additionalRotations = (currentTransforms.rotations - transformsAtCrop.rotations + 4) % 4;
    for (let i = 0; i < additionalRotations; i++) {
      applyCanvasRotation(canvas);
    }
    if (currentTransforms.flipHorizontal !== transformsAtCrop.flipHorizontal) {
      applyCanvasFlipHorizontal(canvas, currentTransforms.rotations);
    }
    if (currentTransforms.flipVertical !== transformsAtCrop.flipVertical) {
      applyCanvasFlipVertical(canvas, currentTransforms.rotations);
    }
  } else if (currentTransforms) {
    if (currentTransforms.rotations) {
      for (let i = 0; i < currentTransforms.rotations; i++) {
        applyCanvasRotation(canvas);
      }
    }
    if (currentTransforms.flipHorizontal) {
      applyCanvasFlipHorizontal(canvas, currentTransforms.rotations);
    }
    if (currentTransforms.flipVertical) {
      applyCanvasFlipVertical(canvas, currentTransforms.rotations);
    }
  }

  setCropData({
    isCropped: false,
    normalizedCropRect: undefined,
    canvasDimensions: undefined,
  });
  setHasPerformedCrop(false);

  canvas.renderAll();
};

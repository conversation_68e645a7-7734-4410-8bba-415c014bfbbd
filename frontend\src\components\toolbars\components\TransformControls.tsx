import React from "react";
import {
  FaRedo,
  FaArrowsAltH,
  FaArrowsAltV,
  FaPalette,
  FaExchangeAlt,
  FaImage,
} from "react-icons/fa";
import type { TransformControlsProps } from "@/shared/types";

const TransformControls: React.FC<TransformControlsProps> = ({
  isShowingOriginal,
  grayscale,
  invert,
  disableGrayscale,
  onRotate,
  onFlipHorizontal,
  onFlipVertical,
  onGrayscaleChange,
  onInvertChange,
  onShowOriginal,
}) => {
  return (
    <>
      <div className="tool-grid">
        <button
          className={`tool-btn ${isShowingOriginal ? "disabled" : ""}`}
          onClick={isShowingOriginal ? undefined : onRotate}
          disabled={isShowingOriginal}
          title="Rotate selected object"
        >
          <FaRedo />
        </button>
        <button
          className={`tool-btn ${isShowingOriginal ? "disabled" : ""}`}
          onClick={isShowingOriginal ? undefined : () => {
            console.log('FLIP BUTTON CLICKED', { isShowingOriginal });
            onFlipHorizontal();
          }}
          disabled={isShowingOriginal}
          title="Flip horizontal"
        >
          <FaArrowsAltH />
        </button>
        <button
          className={`tool-btn ${isShowingOriginal ? "disabled" : ""}`}
          onClick={isShowingOriginal ? undefined : onFlipVertical}
          disabled={isShowingOriginal}
          title="Flip vertical"
        >
          <FaArrowsAltV />
        </button>
        <button
          className={`tool-btn ${grayscale ? "active" : ""} ${
            disableGrayscale || isShowingOriginal ? "disabled" : ""
          }`}
          onClick={() => !disableGrayscale && !isShowingOriginal && onGrayscaleChange(!grayscale)}
          disabled={disableGrayscale || isShowingOriginal}
          title="Toggle grayscale"
        >
          <FaPalette />
        </button>
        <button
          className={`tool-btn ${invert ? "active" : ""} ${isShowingOriginal ? "disabled" : ""}`}
          onClick={() => !isShowingOriginal && onInvertChange(!invert)}
          disabled={isShowingOriginal}
          title="Toggle invert"
        >
          <FaExchangeAlt />
        </button>
        {onShowOriginal && (
          <button
            className={`tool-btn ${isShowingOriginal ? "active" : ""}`}
            onClick={onShowOriginal}
            title={isShowingOriginal ? "Exit show original mode" : "Show original image"}
          >
            <FaImage />
          </button>
        )}
      </div>
    </>
  );
};

export default TransformControls;

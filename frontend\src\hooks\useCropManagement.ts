import { useState } from "react";
import { Canvas } from "fabric";
import { CropData, CropManagementState, UndoTrackingState, TransformState } from "@/shared/types";
import { createCropHandler } from "@/lib/fabric/operations/crop";

export const useCropManagement = (
  fabricCanvas: React.RefObject<Canvas | null>,
  initialCropData: CropData,
  undoTracking: UndoTrackingState,
  containerRef?: React.RefObject<HTMLElement | null>,
  originalImageUrl?: string,
  transformState?: TransformState
): CropManagementState => {
  const [cropData, setCropData] = useState<CropData>(initialCropData);
  const [hasPerformedCrop, setHasPerformedCrop] = useState(initialCropData.isCropped || false);
  const [savedAnnotations, setSavedAnnotations] = useState<any>(null);

  const handleCrop = async () => {
    if (!hasPerformedCrop && fabricCanvas.current) {
      const canvas = fabricCanvas.current;
      const annotations = canvas
        .getObjects()
        .filter(
          (obj) => (obj as any).name !== "cropRect" && (obj as any).name !== "backgroundImage"
        );
      if (annotations.length > 0) {
        const canvasState = canvas.toObject(["name", "id"]);
        if (canvasState.objects) {
          canvasState.objects = canvasState.objects.filter(
            (obj: any) => obj.name !== "cropRect" && obj.name !== "backgroundImage"
          );
        }
        // Remove background image from saved state to avoid conflicts
        delete canvasState.backgroundImage;
        setSavedAnnotations({
          canvasState,
          canvasWidth: canvas.getWidth(),
          canvasHeight: canvas.getHeight(),
        });
      }
    }

    const cropOperation = createCropHandler(
      fabricCanvas,
      hasPerformedCrop,
      setCropData,
      undoTracking.isUndoingRef,
      setHasPerformedCrop,
      containerRef,
      originalImageUrl,
      savedAnnotations,
      transformState,
      cropData
    );

    await cropOperation();

    if (hasPerformedCrop) {
      setSavedAnnotations(null);
    }
  };

  return {
    cropData,
    setCropData,
    hasPerformedCrop,
    setHasPerformedCrop,
    handleCrop,
  };
};

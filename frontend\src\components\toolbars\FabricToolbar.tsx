import React, { useState, useEffect } from "react";
import { ImageToolbarProps, CalibrationData } from "@/shared/types";
import { useFabricTools } from "@/hooks/useFabricTools";
import {
  ToolGrid,
  TransformControls,
  SliderControls,
  GammaControls,
  ActionButtons,
  CalibrationPrompt,
} from "./components";
import CalibrationModal from "./components/CalibrationModal";
import { Rnd } from "react-rnd";
import {
  createCalibrationSubmitHandler,
  createCalibrationCloseHandler,
  getCalibrationFromLocalStorage,
  clearCalibrationFromLocalStorage,
} from "@/lib/fabric/operations/calibration";

const FabricToolbar: React.FC<ImageToolbarProps> = ({
  fabricCanvas,
  fabricConfigs,
  handlers,
  state,
  config = {},
  onShapeCreated,
}) => {
  const { disableGrayscale = false, disableGamma = false } = config;
  const [isCalibrationModalOpen, setCalibrationModalOpen] = useState(false);
  const [isCalibrationPromptOpen, setCalibrationPromptOpen] = useState(false);
  const [localCalibrationData, setLocalCalibrationData] = useState<CalibrationData>(() => {
    const localStorageData = getCalibrationFromLocalStorage();
    return localStorageData ?? fabricConfigs.calibrationData!;
  });
  useEffect(() => {
    if (fabricConfigs.calibrationData) {
      setLocalCalibrationData(fabricConfigs.calibrationData);
    }
    return () => {
      clearCalibrationFromLocalStorage();
    };
  }, [fabricConfigs.calibrationData]);

  const { activeMode, changeToolMode } = useFabricTools({
    fabricCanvas,
    isShowingOriginal: state.isShowingOriginal,
    hasPerformedCrop: state.hasPerformedCrop,
    onShapeCreated,
    onCrop: handlers.actions.handleCrop,
    disableUndoTracking: handlers.tracking.disableUndoTracking,
    enableUndoTracking: handlers.tracking.enableUndoTracking,
    showCalibrationModal: () => setCalibrationModalOpen(true),
    calibrationData: localCalibrationData || undefined,
    onCalibrationPrompt: () => setCalibrationPromptOpen(true),
  });

  return (
    <>
      <Rnd
        default={{
          x: 0,
          y: window.innerHeight / 2 - 150,
          width: 175,
          height: 300,
        }}
        bounds=".viewer-panel"
        enableResizing={false}
      >
        <div className={`fabric-toolbar-vertical ${state.isShowingOriginal ? "disabled" : ""}`}>
          <div className="annotation-tools">
            <ToolGrid
              activeMode={activeMode}
              isShowingOriginal={state.isShowingOriginal}
              hasPerformedCrop={state.hasPerformedCrop}
              onToolSelect={changeToolMode}
              onCrop={handlers.actions.handleCrop}
            />
            <TransformControls
              isShowingOriginal={state.isShowingOriginal}
              grayscale={fabricConfigs.grayscale}
              invert={fabricConfigs.invert}
              disableGrayscale={disableGrayscale}
              onRotate={handlers.transform.handleRotate}
              onFlipHorizontal={handlers.transform.handleFlipHorizontal}
              onFlipVertical={handlers.transform.handleFlipVertical}
              onGrayscaleChange={handlers.filter.handleGrayscaleChange}
              onInvertChange={handlers.filter.handleInvertChange}
              onShowOriginal={handlers.actions.handleShowOriginal}
            />
          </div>
          <SliderControls
            brightness={fabricConfigs.brightness}
            contrast={fabricConfigs.contrast}
            sharpness={fabricConfigs.sharpness}
            isShowingOriginal={state.isShowingOriginal}
            onBrightnessChange={handlers.filter.handleBrightnessChange}
            onContrastChange={handlers.filter.handleContrastChange}
            onSharpnessChange={handlers.filter.handleSharpnessChange}
          />
          <GammaControls
            gammaR={fabricConfigs.gammaR}
            gammaG={fabricConfigs.gammaG}
            gammaB={fabricConfigs.gammaB}
            disableGamma={disableGamma}
            isShowingOriginal={state.isShowingOriginal}
            onGammaRChange={handlers.filter.handleGammaRChange}
            onGammaGChange={handlers.filter.handleGammaGChange}
            onGammaBChange={handlers.filter.handleGammaBChange}
          />
          <ActionButtons
            canUndo={state.canUndo}
            isShowingOriginal={state.isShowingOriginal}
            onUndo={handlers.actions.handleUndo}
            onSave={handlers.actions.handleSave}
          />
        </div>
      </Rnd>
      <CalibrationModal
        isOpen={isCalibrationModalOpen}
        onClose={createCalibrationCloseHandler(fabricCanvas?.current, setCalibrationModalOpen)}
        onSubmit={createCalibrationSubmitHandler(fabricCanvas?.current, () => {
          const newCalibrationData = getCalibrationFromLocalStorage();
          setLocalCalibrationData(newCalibrationData ?? fabricConfigs.calibrationData!);
          setCalibrationModalOpen(false);
          setTimeout(() => changeToolMode("measure"), 1000);
        })}
      />
      <CalibrationPrompt
        isOpen={isCalibrationPromptOpen}
        onClose={() => setCalibrationPromptOpen(false)}
        onCalibrate={() => {
          setCalibrationPromptOpen(false);
          changeToolMode("calibrate");
        }}
      />
    </>
  );
};

export default FabricToolbar;

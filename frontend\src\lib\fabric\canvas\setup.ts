import { Canvas } from "fabric";
import { FilterParams, SetupCanvasParams } from "@/shared/types";
import { loadAnnotations } from "../operations/annotations";
import { applyCanvasFilters } from "../operations/filters";
import { loadCanvasImage } from "../rendering/image";
import { createImageLoadContainer, applyCropToCanvas } from "../operations/crop";
import {
  applyCanvasRotation,
  applyCanvasFlipHorizontal,
  applyCanvasFlipVertical,
} from "../operations/transforms";

const canvasFilterStates = new Map<Canvas, FilterParams>();

export const setupImageCanvas = async ({
  canvasElement,
  imageUrl,
  annotations,
  filters,
  cropData,
  existingCanvas,
  transformState,
}: SetupCanvasParams): Promise<{
  canvas: Canvas;
}> => {
  const finalImageSource = imageUrl;

  const applyTransforms = () => {
    if (transformState?.rotations) {
      for (let i = 0; i < transformState.rotations; i++) {
        applyCanvasRotation(canvas);
      }
    }
    if (transformState?.flipHorizontal) {
      applyCanvasFlipHorizontal(canvas);
    }
    if (transformState?.flipVertical) {
      applyCanvasFlipVertical(canvas);
    }
  };
  if (existingCanvas) {
    existingCanvas.dispose();
  }

  const canvas = new Canvas(canvasElement, {
    selection: true,
    backgroundColor: "transparent",
  });

  const containerRect = canvasElement.parentElement?.getBoundingClientRect();

  if (cropData?.isCropped && cropData.normalizedCropRect) {
    const imageLoadContainer = createImageLoadContainer(cropData, containerRect);

    await loadCanvasImage(canvas, finalImageSource, {
      containerRect: imageLoadContainer || undefined,
    });

    const transformsAtCrop = cropData.transformStateAtCrop;
    const currentTransforms = transformState;

    if (transformsAtCrop) {
      if (transformsAtCrop.rotations) {
        for (let i = 0; i < transformsAtCrop.rotations; i++) {
          applyCanvasRotation(canvas);
        }
      }
      if (transformsAtCrop.flipHorizontal) {
        applyCanvasFlipHorizontal(canvas);
      }
      if (transformsAtCrop.flipVertical) {
        applyCanvasFlipVertical(canvas);
      }
    }

    await applyCropToCanvas(canvas, cropData);

    if (currentTransforms && transformsAtCrop) {
      const additionalRotations =
        (currentTransforms.rotations - transformsAtCrop.rotations + 4) % 4;
      for (let i = 0; i < additionalRotations; i++) {
        applyCanvasRotation(canvas);
      }
      if (currentTransforms.flipHorizontal !== transformsAtCrop.flipHorizontal) {
        applyCanvasFlipHorizontal(canvas);
      }
      if (currentTransforms.flipVertical !== transformsAtCrop.flipVertical) {
        applyCanvasFlipVertical(canvas);
      }
    }
  } else {
    await loadCanvasImage(canvas, finalImageSource, {
      containerRect: containerRect || undefined,
    });
    applyTransforms();
  }

  if (filters) {
    canvasFilterStates.set(canvas, { ...filters });
    canvas.renderAll();
    applyCanvasFilters(canvas, filters);
  }

  if (annotations) {
    await loadAnnotations(canvas, annotations);
  }

  canvas.selection = false;
  canvas.forEachObject((obj) => {
    const objName = (obj as unknown as Record<string, unknown>)?.name;
    if (objName !== "backgroundImage") {
      obj.selectable = false;
      obj.evented = false;
    }
  });

  return {
    canvas,
  };
};

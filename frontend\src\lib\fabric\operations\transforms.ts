import { Canvas, Point } from "fabric";
import { TransformState } from "@/shared/types";

export const applyCanvasRotation = (canvas: Canvas, onRotationComplete?: () => void): void => {
  const currentWidth = canvas.getWidth();
  const currentHeight = canvas.getHeight();

  // Rotate canvas dimensions
  canvas.setDimensions({
    width: currentHeight,
    height: currentWidth,
  });

  // Rotate background image if any
  if (canvas.backgroundImage) {
    const bgImage = canvas.backgroundImage;
    const currentAngle = bgImage.angle || 0;
    const newAngle = (currentAngle + 90) % 360;

    bgImage.set({
      angle: newAngle,
      originX: "center",
      originY: "center",
      left: canvas.width! / 2,
      top: canvas.height! / 2,
    });
  }

  const oldCenter = new Point(currentWidth / 2, currentHeight / 2);
  const newCenter = new Point(currentHeight / 2, currentWidth / 2);

  canvas.getObjects().forEach((obj) => {
    const objCenter = obj.getCenterPoint();

    // Translate to origin
    const dx = objCenter.x - oldCenter.x;
    const dy = objCenter.y - oldCenter.y;

    // Rotate 90° clockwise
    const newX = newCenter.x - dy;
    const newY = newCenter.y + dx;

    obj.set({
      left: newX,
      top: newY,
      angle: (obj.angle || 0) + 90,
      originX: "center",
      originY: "center",
    });

    obj.setCoords();
  });

  canvas.requestRenderAll();
  if (onRotationComplete) {
    onRotationComplete();
  }
};

export const applyCanvasFlipHorizontal = (canvas: Canvas): void => {
  const centerX = canvas.getWidth() / 2;

  if (canvas.backgroundImage) {
    canvas.backgroundImage.set("flipX", !canvas.backgroundImage.flipX);
  }

  canvas.getObjects().forEach((obj) => {
    if (obj.type === "line") {
      const line = obj as any;

      // Mirror line x-coordinates
      line.x1 = -line.x1;
      line.x2 = -line.x2;

      // Flip around canvas center
      const distance = line.left! - centerX;
      line.left = centerX - distance;

      line.flipX = !line.flipX;
    } else {
      const px = obj.left! + obj.getScaledWidth() / 2;
      const distance = px - centerX;
      const newPx = centerX - distance;

      obj.set({
        left: newPx - obj.getScaledWidth() / 2,
        flipX: !obj.flipX,
      });
    }

    obj.setCoords();
  });

  canvas.requestRenderAll();
};

export const applyCanvasFlipVertical = (canvas: Canvas): void => {
  const centerY = canvas.getHeight() / 2;

  if (canvas.backgroundImage) {
    canvas.backgroundImage.set("flipY", !canvas.backgroundImage.flipY);
  }

  canvas.getObjects().forEach((obj) => {
    if (obj.type === "line") {
      const line = obj as any;

      // Mirror line y-coordinates
      line.y1 = -line.y1;
      line.y2 = -line.y2;

      const distance = line.top! - centerY;
      line.top = centerY - distance;

      line.flipY = !line.flipY;
    } else {
      const py = obj.top! + obj.getScaledHeight() / 2;
      const distance = py - centerY;
      const newPy = centerY - distance;

      obj.set({
        top: newPy - obj.getScaledHeight() / 2,
        flipY: !obj.flipY,
      });
    }
    obj.setCoords();
  });
  canvas.requestRenderAll();
};

export const createRotateHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  setTransformState: React.Dispatch<React.SetStateAction<TransformState>>,
  onRotationComplete?: () => void
) => {
  return () => {
    if (fabricCanvas?.current) {
      applyCanvasRotation(fabricCanvas.current, onRotationComplete);
      setTransformState((prev) => ({ ...prev, rotations: (prev.rotations + 1) % 4 }));
    }
  };
};

export const createFlipHorizontalHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  setTransformState: React.Dispatch<React.SetStateAction<TransformState>>
) => {
  return () => {
    if (fabricCanvas?.current) {
      applyCanvasFlipHorizontal(fabricCanvas.current);
      setTransformState((prev) => ({ ...prev, flipHorizontal: !prev.flipHorizontal }));
    }
  };
};

export const createFlipVerticalHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  setTransformState: React.Dispatch<React.SetStateAction<TransformState>>
) => {
  return () => {
    if (fabricCanvas?.current) {
      applyCanvasFlipVertical(fabricCanvas.current);
      setTransformState((prev) => ({ ...prev, flipVertical: !prev.flipVertical }));
    }
  };
};
